# 大乐透数据分析系统完整实现 - PRP (Product Requirements Prompt)

## Goal
实现一个完整的大乐透数据分析系统，基于上下文工程优化的架构设计，采用高耦合低内聚的简单单体应用模式，提供数据管理、分析计算、结果展示等核心功能。

## Why
- **业务价值**: 为大乐透数据分析提供专业的工具平台，支持开奖数据管理和模式分析
- **技术示范**: 展示上下文工程在实际项目中的应用，验证简化架构的有效性
- **算法统一**: 确保增量计算和普通计算使用同一套算法，保证数据一致性
- **简单部署**: 单文件应用设计，便于部署和维护

## What
构建一个基于Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2的大乐透数据分析系统，包含：

### 核心功能模块
1. **数据管理**: 开奖数据的增删改查、批量删除、数据更新
2. **数据分析**: 奇偶排布、分区比值、红球间隔、蓝球间距分析
3. **数据展示**: 简单的HTML页面展示分析结果
4. **系统管理**: 基础的数据库操作和文件管理

### Success Criteria
- [ ] 完整的数据管理功能（增删改查、批量操作）
- [ ] 准确的数据分析功能（奇偶排布、分区比值、历史追踪）
- [ ] 清晰的数据展示界面（主页、数据列表、分析结果）
- [ ] 算法一致性验证通过（增量计算=普通计算）
- [ ] 遵循高耦合低内聚的架构设计
- [ ] 所有测试通过，代码质量符合规范

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://docs.python.org/3.13/
  why: Python 3.13语法特性和标准库使用
  
- url: https://flask.palletsprojects.com/
  why: Flask应用结构、路由设计、模板渲染
  
- url: https://docs.sqlalchemy.org/
  why: SQLAlchemy ORM模型定义和数据库操作
  
- file: context-engineering-intro-main/examples/backend/lottery_app.py
  why: Flask应用主文件模式（高耦合设计），路由和视图函数实现
  
- file: context-engineering-intro-main/examples/utils/calculations.py
  why: 统一计算算法模式，确保算法一致性的实现方法
  
- file: context-engineering-intro-main/examples/templates/index.html
  why: 简洁的页面模板模式，响应式设计和用户界面
  
- file: context-engineering-intro-main/CLAUDE.md
  why: 项目架构规范、代码风格约定、开发规则
  
- file: context-engineering-intro-main/INITIAL.md
  why: 详细的文件目录设计、业务规则说明、技术实现要求
```

### Current Codebase tree
```bash
context-engineering-intro-main/
├── examples/                  # 代码模式和最佳实践
│   ├── backend/
│   │   └── lottery_app.py    # Flask应用主文件模式
│   ├── templates/
│   │   └── index.html        # 页面模板模式
│   ├── utils/
│   │   └── calculations.py   # 统一计算算法模式
│   └── README.md             # 示例说明文档
├── PRPs/                     # 产品需求提示
│   └── templates/
│       └── prp_base.md       # PRP基础模板
├── CLAUDE.md                 # AI助手全局规则
├── INITIAL.md               # 功能需求模板
└── README.md                # 项目说明文档
```

### Desired Codebase tree with files to be added
```bash
lottery-analysis-system/
├── app.py                      # Flask应用主文件（高耦合设计，包含所有功能）
├── calculations.py             # 统一计算算法模块
├── requirements.txt            # Python依赖包
├── README.md                   # 项目说明文档
├── lottery.db                  # SQLite数据库文件（运行时生成）
├── templates/                  # Jinja2模板目录
│   ├── base.html              # 基础模板
│   ├── index.html             # 主页模板
│   ├── data_list.html         # 数据列表模板
│   ├── add_data.html          # 添加数据模板
│   └── analysis.html          # 分析结果模板
├── static/                     # 静态文件目录
│   ├── css/
│   │   └── style.css          # 样式文件
│   └── js/
│       └── main.js            # JavaScript文件
├── logs/                       # 日志文件目录
│   ├── app.log                # 应用日志
│   └── error.log              # 错误日志
└── tests/                      # 测试文件目录
    ├── test_calculations.py   # 算法一致性测试
    ├── test_database.py       # 数据库操作测试
    └── test_app.py            # 应用功能测试
```

### Known Gotchas & Library Quirks
```python
# CRITICAL: 大乐透业务规则约束
# 红球范围：1-35，每期选5个不重复号码，按升序排列
# 蓝球范围：1-12，每期选2个不重复号码，按升序排列
# 奇偶排布：基于排序后红球计算，格式如"奇偶奇奇偶"
# 分区比值：35个红球分7区(1-5,6-10,11-15,16-20,21-25,26-30,31-35)

# CRITICAL: 算法一致性要求
# 增量计算和普通计算必须使用完全相同的算法
# 所有计算逻辑集中在calculations.py中
# 历史追踪算法需要处理首次出现和边界情况

# CRITICAL: 高耦合低内聚设计原则
# 功能集中在少数文件中，避免过度抽象
# app.py包含所有路由、视图函数和核心业务逻辑
# 数据库操作函数直接在主文件中定义

# CRITICAL: SQLAlchemy配置注意事项
# 使用SQLite数据库，注意日期字段的处理
# 数据库会话管理，确保正确关闭连接
# 批量操作时注意事务处理

# CRITICAL: Flask应用配置
# 使用环境变量配置主机、端口、调试模式
# 日志配置使用RotatingFileHandler
# 模板和静态文件路径配置
```

## Implementation Blueprint

### Data models and structure
```python
# SQLAlchemy数据模型 - 在app.py中定义（高耦合设计）
class LotteryData(Base):
    __tablename__ = 'lottery_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    period = Column(Integer, unique=True, nullable=False)
    date = Column(Date, nullable=False)
    red_ball_1 = Column(Integer, nullable=False)
    red_ball_2 = Column(Integer, nullable=False)
    red_ball_3 = Column(Integer, nullable=False)
    red_ball_4 = Column(Integer, nullable=False)
    red_ball_5 = Column(Integer, nullable=False)
    blue_ball_1 = Column(Integer, nullable=False)
    blue_ball_2 = Column(Integer, nullable=False)
    # 计算字段
    odd_even_pattern = Column(String(10))
    zone_ratio = Column(String(20))
    red_ball_sum = Column(Integer)
    blue_ball_distance = Column(Integer)
    # 历史追踪字段
    last_odd_even_period = Column(Integer)
    last_odd_even_date = Column(Date)
    odd_even_interval = Column(Integer)
    last_zone_ratio_period = Column(Integer)
    last_zone_ratio_date = Column(Date)
    zone_ratio_interval = Column(Integer)
```

### List of tasks to be completed in order

```yaml
Task 1: 项目环境和基础设置
CREATE lottery-analysis-system/ directory structure:
  - MIRROR pattern from: context-engineering-intro-main/INITIAL.md (lines 63-88)
  - CREATE requirements.txt with Flask, SQLAlchemy, Jinja2
  - CREATE README.md with project description and setup instructions

Task 2: 统一计算算法模块实现
CREATE calculations.py:
  - MIRROR pattern from: context-engineering-intro-main/examples/utils/calculations.py
  - IMPLEMENT LotteryCalculations class with all calculation methods
  - ENSURE algorithm consistency for incremental and batch calculations
  - INCLUDE historical tracking algorithms for pattern intervals

Task 3: Flask应用主文件实现
CREATE app.py:
  - MIRROR pattern from: context-engineering-intro-main/examples/backend/lottery_app.py
  - IMPLEMENT high-coupling design with all routes and business logic
  - INCLUDE database model definition and operations
  - IMPLEMENT all CRUD operations and analysis functions

Task 4: HTML模板文件实现
CREATE templates/ directory with all template files:
  - MIRROR pattern from: context-engineering-intro-main/examples/templates/index.html
  - CREATE base.html, index.html, data_list.html, add_data.html, analysis.html
  - IMPLEMENT responsive design and clean user interface

Task 5: 静态文件和样式实现
CREATE static/ directory with CSS and JavaScript:
  - CREATE style.css with clean, professional styling
  - CREATE main.js with basic form validation and interactions
  - ENSURE responsive design for mobile devices

Task 6: 测试文件实现
CREATE tests/ directory with comprehensive test coverage:
  - CREATE test_calculations.py for algorithm consistency testing
  - CREATE test_database.py for database operations testing
  - CREATE test_app.py for Flask application testing
  - FOCUS on algorithm consistency validation
```

### Per task pseudocode

```python
# Task 2: 统一计算算法模块 - 关键实现细节
class LotteryCalculations:
    # PATTERN: 所有计算方法都是静态方法，确保无状态
    @staticmethod
    def calculate_odd_even_pattern(red_balls: List[int]) -> str:
        # CRITICAL: 必须先排序红球，然后计算奇偶
        sorted_balls = sorted(red_balls)
        return "".join("奇" if ball % 2 == 1 else "偶" for ball in sorted_balls)

    @staticmethod
    def calculate_zone_ratio(red_balls: List[int]) -> str:
        # CRITICAL: 35个红球分7区，每区5个号码
        zone_counts = [0] * 7
        for ball in red_balls:
            zone_index = (ball - 1) // 5
            zone_counts[zone_index] += 1
        return ":".join(map(str, zone_counts))

    @classmethod
    def calculate_all_fields(cls, red_balls, blue_balls, period=None, history=None):
        # PATTERN: 统一入口，确保所有计算使用相同算法
        # CRITICAL: 历史追踪需要遍历历史数据找到上次相同模式
        basic_fields = {
            'odd_even_pattern': cls.calculate_odd_even_pattern(red_balls),
            'zone_ratio': cls.calculate_zone_ratio(red_balls),
            'red_ball_sum': sum(red_balls),
            'blue_ball_distance': abs(blue_balls[1] - blue_balls[0])
        }

        # GOTCHA: 历史追踪算法需要处理首次出现的情况
        if history and period:
            tracking_fields = cls.calculate_historical_tracking(
                basic_fields, period, history
            )
            basic_fields.update(tracking_fields)

        return basic_fields

# Task 3: Flask应用主文件 - 关键实现细节
@app.route('/add', methods=['GET', 'POST'])
def add_data():
    # PATTERN: 使用统一算法计算所有字段
    if request.method == 'POST':
        # CRITICAL: 数据验证使用统一验证方法
        red_balls = [int(request.form[f'red_ball_{i}']) for i in range(1, 6)]
        blue_balls = [int(request.form[f'blue_ball_{i}']) for i in range(1, 3)]

        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        if not is_valid:
            flash(f'数据验证失败: {error_msg}')
            return render_template('add_data.html')

        # CRITICAL: 插入数据时使用统一算法
        success = insert_lottery_data(period, date, red_balls, blue_balls)

def recalculate_all_data():
    # CRITICAL: 重新计算用于验证算法一致性
    # 必须按期号升序处理，确保历史追踪正确
    all_data = session.query(LotteryData).order_by(LotteryData.period.asc()).all()
    processed_history = []

    for item in all_data:
        # PATTERN: 使用完全相同的算法重新计算
        calculated_fields = LotteryCalculations.calculate_all_fields(
            red_balls, blue_balls, item.period, processed_history
        )
        # 更新数据库记录
        # 添加到历史记录供后续使用
```

### Integration Points
```yaml
DATABASE:
  - migration: "CREATE TABLE lottery_data with all required columns"
  - indexes: "CREATE INDEX on period, date for performance"

CONFIG:
  - add to: app.py
  - pattern: "DATABASE_URL = 'sqlite:///lottery.db'"
  - pattern: "app.secret_key = os.environ.get('SECRET_KEY', 'default-key')"

ROUTES:
  - implement in: app.py (high-coupling design)
  - routes: "/", "/data", "/add", "/analysis", "/recalculate"
  - pattern: "All routes in single file with business logic"

TEMPLATES:
  - base template: templates/base.html with common layout
  - extend pattern: "{% extends 'base.html' %}" in all templates
  - data binding: "{{ data.period }}" for lottery data display
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
python -m py_compile app.py calculations.py  # Syntax check
python -m flake8 app.py calculations.py --max-line-length=100  # Style check

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests
```python
# CREATE test_calculations.py with algorithm consistency tests:
def test_algorithm_consistency():
    """验证增量计算和普通计算结果一致"""
    # 使用相同数据测试两种计算方式
    red_balls = [1, 8, 15, 22, 29]
    blue_balls = [3, 7]

    # 增量计算结果
    incremental_result = LotteryCalculations.calculate_all_fields(red_balls, blue_balls)

    # 普通计算结果
    batch_result = LotteryCalculations.calculate_all_fields(red_balls, blue_balls)

    assert incremental_result == batch_result

def test_odd_even_pattern_calculation():
    """验证奇偶排布计算正确性"""
    red_balls = [1, 8, 15, 22, 29]  # 奇偶奇偶奇
    result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
    assert result == "奇偶奇偶奇"

def test_zone_ratio_calculation():
    """验证分区比值计算正确性"""
    red_balls = [1, 8, 15, 22, 29]  # 分布在5个不同区
    result = LotteryCalculations.calculate_zone_ratio(red_balls)
    assert result == "1:1:1:1:1:0:0"
```

```bash
# Run and iterate until passing:
python -m pytest tests/ -v
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test
```bash
# Start the Flask application
python app.py &

# Test main routes
curl http://localhost:5000/
curl http://localhost:5000/data
curl http://localhost:5000/analysis

# Test data addition (POST request)
curl -X POST http://localhost:5000/add \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "period=24001&date=2024-01-01&red_ball_1=1&red_ball_2=8&red_ball_3=15&red_ball_4=22&red_ball_5=29&blue_ball_1=3&blue_ball_2=7"

# Expected: Successful responses with proper HTML content
# If error: Check logs/app.log for detailed error information
```

### Level 4: Algorithm Consistency Validation
```bash
# Test algorithm consistency through recalculation
curl http://localhost:5000/recalculate

# Verify database consistency
python -c "
from app import get_all_lottery_data
data = get_all_lottery_data()
print(f'Total records: {len(data)}')
print('Sample record:', data[0] if data else 'No data')
"

# Expected: All calculations consistent, no data corruption
```

## Final validation Checklist
- [ ] All tests pass: `python -m pytest tests/ -v`
- [ ] No syntax errors: `python -m py_compile app.py calculations.py`
- [ ] No style violations: `python -m flake8 app.py calculations.py`
- [ ] Flask app starts successfully: `python app.py`
- [ ] All routes accessible: curl tests pass
- [ ] Algorithm consistency verified: recalculation produces same results
- [ ] Database operations work: CRUD operations successful
- [ ] Templates render correctly: HTML pages display properly
- [ ] Error handling works: Invalid data rejected gracefully
- [ ] Logs are informative: Check logs/app.log for proper logging

---

## Anti-Patterns to Avoid
- ❌ Don't create separate service layers - use high-coupling design
- ❌ Don't use different algorithms for incremental vs batch calculations
- ❌ Don't skip data validation - always validate lottery data format
- ❌ Don't ignore historical tracking - implement pattern interval calculation
- ❌ Don't use complex ORM relationships - keep database model simple
- ❌ Don't create multiple configuration files - keep config in main app file
- ❌ Don't skip algorithm consistency tests - this is critical for data integrity

## PRP Quality Score: 8/10

**Confidence Level for One-Pass Implementation**: High

**Reasoning**:
- Complete context provided with detailed code examples
- Clear task breakdown with specific implementation patterns
- Comprehensive validation strategy with executable tests
- Detailed business rules and technical constraints
- Proven architecture patterns from existing examples

**Potential Risk Areas**:
- Historical tracking algorithm complexity may require iteration
- Template styling details may need refinement
- Database migration edge cases may need handling
