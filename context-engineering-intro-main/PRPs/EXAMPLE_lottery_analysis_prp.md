# 大乐透号码热度分析功能 - PRP (Product Requirements Prompt)

## 📋 功能概述

实现大乐透号码热度分析功能，统计每个号码在最近N期中的出现频率，并提供可视化图表展示热度趋势。该功能将帮助用户了解号码的冷热程度，为选号提供参考。

## 🎯 业务需求

### 核心功能
1. **热度统计**: 统计红球(1-35)和蓝球(1-12)在指定期数内的出现频率
2. **热度排行**: 提供号码热度排行榜，支持升序/降序排列
3. **趋势分析**: 展示号码热度的时间趋势变化
4. **可视化展示**: 使用图表直观展示热度分布和趋势
5. **参数配置**: 支持自定义分析期数、热度阈值等参数

### 业务规则
- 红球范围：1-35，每期选5个不重复号码
- 蓝球范围：1-12，每期选2个不重复号码
- 热度计算：出现次数 / 总期数 * 100%
- 热度分级：热号(>平均值+1σ)、温号(平均值±1σ)、冷号(<平均值-1σ)
- 默认分析期数：100期

## 🏗️ 技术实现

### 后端实现

#### 1. 数据模型扩展
```python
# 在 lottery_models.py 中添加热度分析缓存表
class NumberHeatCache(db.Model):
    __tablename__ = 'number_heat_cache'
    
    id = db.Column(db.Integer, primary_key=True)
    ball_type = db.Column(db.String(10), nullable=False)  # 'red' or 'blue'
    ball_number = db.Column(db.Integer, nullable=False)
    periods = db.Column(db.Integer, nullable=False)  # 分析期数
    frequency = db.Column(db.Integer, nullable=False)  # 出现次数
    heat_percentage = db.Column(db.Float, nullable=False)  # 热度百分比
    heat_level = db.Column(db.String(10), nullable=False)  # 'hot', 'warm', 'cold'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        db.Index('idx_heat_cache_lookup', 'ball_type', 'periods', 'ball_number'),
    )
```

#### 2. 业务服务层
```python
# 在 lottery_service.py 中添加热度分析服务
class HeatAnalysisService:
    def __init__(self, lottery_repo):
        self.lottery_repo = lottery_repo
    
    def calculate_number_heat(self, periods: int = 100) -> Dict[str, Any]:
        """
        计算号码热度
        
        Args:
            periods: 分析期数
            
        Returns:
            Dict: 包含红球和蓝球热度数据
        """
        # 获取最近N期数据
        recent_data = self.lottery_repo.get_recent_data(periods)
        
        # 计算红球热度
        red_heat = self._calculate_ball_heat(recent_data, 'red', 35)
        
        # 计算蓝球热度
        blue_heat = self._calculate_ball_heat(recent_data, 'blue', 12)
        
        return {
            'red_balls': red_heat,
            'blue_balls': blue_heat,
            'analysis_periods': periods,
            'total_draws': len(recent_data),
            'analysis_date': datetime.now().isoformat()
        }
    
    def _calculate_ball_heat(self, data: List[Dict], ball_type: str, max_number: int) -> List[Dict]:
        """计算指定类型球的热度"""
        # 统计每个号码的出现次数
        frequency = defaultdict(int)
        
        for record in data:
            if ball_type == 'red':
                balls = [record[f'red_ball_{i}'] for i in range(1, 6)]
            else:
                balls = [record[f'blue_ball_{i}'] for i in range(1, 3)]
            
            for ball in balls:
                frequency[ball] += 1
        
        # 计算热度百分比和分级
        total_periods = len(data)
        heat_data = []
        
        for number in range(1, max_number + 1):
            freq = frequency[number]
            heat_percentage = (freq / total_periods) * 100 if total_periods > 0 else 0
            
            heat_data.append({
                'number': number,
                'frequency': freq,
                'heat_percentage': round(heat_percentage, 2),
                'heat_level': self._classify_heat_level(heat_percentage, ball_type, max_number)
            })
        
        return sorted(heat_data, key=lambda x: x['heat_percentage'], reverse=True)
    
    def _classify_heat_level(self, percentage: float, ball_type: str, max_number: int) -> str:
        """分类热度等级"""
        # 理论平均值
        if ball_type == 'red':
            avg = (5 / 35) * 100  # 约14.29%
        else:
            avg = (2 / 12) * 100  # 约16.67%
        
        if percentage > avg * 1.2:
            return 'hot'
        elif percentage < avg * 0.8:
            return 'cold'
        else:
            return 'warm'
```

#### 3. API控制器
```python
# 在 lottery_controller.py 中添加热度分析接口
@lottery_bp.route('/heat-analysis', methods=['GET'])
def get_heat_analysis():
    """获取号码热度分析"""
    try:
        periods = request.args.get('periods', 100, type=int)
        ball_type = request.args.get('type', 'all')  # 'red', 'blue', 'all'
        
        # 参数验证
        if periods <= 0 or periods > 1000:
            raise ValidationError("分析期数必须在1-1000之间")
        
        # 调用热度分析服务
        heat_service = HeatAnalysisService(lottery_repo)
        result = heat_service.calculate_number_heat(periods)
        
        # 根据类型过滤结果
        if ball_type == 'red':
            result = {'red_balls': result['red_balls'], **{k: v for k, v in result.items() if k != 'blue_balls'}}
        elif ball_type == 'blue':
            result = {'blue_balls': result['blue_balls'], **{k: v for k, v in result.items() if k != 'red_balls'}}
        
        return create_success_response(data=result)
        
    except ValidationError as e:
        return create_error_response(str(e), 400)
    except Exception as e:
        return create_error_response(f"热度分析失败: {str(e)}", 500)
```

### 前端实现

#### 1. 热度分析组件
```jsx
// HeatAnalysis.jsx
import React, { useState, useEffect, useMemo } from 'react';
import { Card, Row, Col, Select, Spin, Table, Progress } from 'antd';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const HeatAnalysis = () => {
  const [loading, setLoading] = useState(false);
  const [heatData, setHeatData] = useState(null);
  const [periods, setPeriods] = useState(100);
  const [ballType, setBallType] = useState('all');

  // 获取热度数据
  const fetchHeatData = async () => {
    setLoading(true);
    try {
      const response = await lotteryService.getHeatAnalysis(periods, ballType);
      setHeatData(response.data);
    } catch (error) {
      message.error('获取热度数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHeatData();
  }, [periods, ballType]);

  // 热度等级颜色映射
  const getHeatColor = (level) => {
    switch (level) {
      case 'hot': return '#ff4d4f';
      case 'warm': return '#faad14';
      case 'cold': return '#1890ff';
      default: return '#d9d9d9';
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '号码',
      dataIndex: 'number',
      key: 'number',
      render: (number, record) => (
        <span style={{ 
          color: getHeatColor(record.heat_level),
          fontWeight: 'bold',
          fontSize: '16px'
        }}>
          {String(number).padStart(2, '0')}
        </span>
      )
    },
    {
      title: '出现次数',
      dataIndex: 'frequency',
      key: 'frequency',
      sorter: (a, b) => a.frequency - b.frequency
    },
    {
      title: '热度',
      dataIndex: 'heat_percentage',
      key: 'heat_percentage',
      render: (percentage, record) => (
        <Progress
          percent={percentage}
          size="small"
          strokeColor={getHeatColor(record.heat_level)}
          format={() => `${percentage}%`}
        />
      ),
      sorter: (a, b) => a.heat_percentage - b.heat_percentage
    },
    {
      title: '等级',
      dataIndex: 'heat_level',
      key: 'heat_level',
      render: (level) => (
        <span style={{ color: getHeatColor(level) }}>
          {level === 'hot' ? '热号' : level === 'warm' ? '温号' : '冷号'}
        </span>
      ),
      filters: [
        { text: '热号', value: 'hot' },
        { text: '温号', value: 'warm' },
        { text: '冷号', value: 'cold' }
      ],
      onFilter: (value, record) => record.heat_level === value
    }
  ];

  return (
    <div className="heat-analysis">
      <Card title="号码热度分析" extra={
        <Space>
          <Select
            value={periods}
            onChange={setPeriods}
            style={{ width: 120 }}
          >
            <Option value={50}>最近50期</Option>
            <Option value={100}>最近100期</Option>
            <Option value={200}>最近200期</Option>
          </Select>
          <Select
            value={ballType}
            onChange={setBallType}
            style={{ width: 100 }}
          >
            <Option value="all">全部</Option>
            <Option value="red">红球</Option>
            <Option value="blue">蓝球</Option>
          </Select>
        </Space>
      }>
        <Spin spinning={loading}>
          {heatData && (
            <Row gutter={[16, 16]}>
              {/* 红球热度 */}
              {(ballType === 'all' || ballType === 'red') && heatData.red_balls && (
                <Col span={ballType === 'all' ? 12 : 24}>
                  <Card title="红球热度" size="small">
                    <Table
                      dataSource={heatData.red_balls}
                      columns={columns}
                      rowKey="number"
                      pagination={{ pageSize: 10 }}
                      size="small"
                    />
                  </Card>
                </Col>
              )}
              
              {/* 蓝球热度 */}
              {(ballType === 'all' || ballType === 'blue') && heatData.blue_balls && (
                <Col span={ballType === 'all' ? 12 : 24}>
                  <Card title="蓝球热度" size="small">
                    <Table
                      dataSource={heatData.blue_balls}
                      columns={columns}
                      rowKey="number"
                      pagination={{ pageSize: 6 }}
                      size="small"
                    />
                  </Card>
                </Col>
              )}
              
              {/* 热度分布图表 */}
              <Col span={24}>
                <Card title="热度分布图" size="small">
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={ballType === 'blue' ? heatData.blue_balls : heatData.red_balls}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="number" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="heat_percentage" fill="#1890ff" />
                    </BarChart>
                  </ResponsiveContainer>
                </Card>
              </Col>
            </Row>
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default HeatAnalysis;
```

#### 2. API服务扩展
```javascript
// 在 lotteryService.js 中添加热度分析方法
async getHeatAnalysis(periods = 100, type = 'all') {
  try {
    const url = `${ENDPOINTS.LOTTERY_DATA}/heat-analysis`;
    const response = await this.api.get(url, {
      params: { periods, type }
    });
    return response;
  } catch (error) {
    throw new ApiError(
      `获取热度分析失败: ${error.message}`,
      error.type || ERROR_TYPES.SERVER,
      error.status
    );
  }
}
```

## 🧪 测试用例

### 后端测试
```python
def test_heat_analysis_calculation():
    """测试热度分析计算"""
    # 准备测试数据
    test_data = [
        {'red_ball_1': 1, 'red_ball_2': 8, 'red_ball_3': 15, 'red_ball_4': 22, 'red_ball_5': 29,
         'blue_ball_1': 3, 'blue_ball_2': 7},
        # ... 更多测试数据
    ]
    
    heat_service = HeatAnalysisService(mock_repo)
    result = heat_service.calculate_number_heat(periods=10)
    
    assert 'red_balls' in result
    assert 'blue_balls' in result
    assert len(result['red_balls']) == 35
    assert len(result['blue_balls']) == 12

def test_heat_analysis_api():
    """测试热度分析API"""
    response = client.get('/api/heat-analysis?periods=100&type=red')
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] is True
    assert 'red_balls' in data['data']
```

### 前端测试
```javascript
describe('HeatAnalysis Component', () => {
  test('renders heat analysis table', () => {
    render(<HeatAnalysis />);
    expect(screen.getByText('号码热度分析')).toBeInTheDocument();
  });

  test('filters by ball type', async () => {
    render(<HeatAnalysis />);
    
    const select = screen.getByDisplayValue('全部');
    fireEvent.change(select, { target: { value: 'red' } });
    
    await waitFor(() => {
      expect(screen.getByText('红球热度')).toBeInTheDocument();
    });
  });
});
```

## ✅ 验收标准

1. **功能完整性**
   - [ ] 能够正确计算红球和蓝球的热度
   - [ ] 支持自定义分析期数
   - [ ] 提供热度排行榜和分级
   - [ ] 图表展示直观清晰

2. **性能要求**
   - [ ] API响应时间 < 1秒
   - [ ] 支持1000期以内的数据分析
   - [ ] 前端渲染流畅，无卡顿

3. **用户体验**
   - [ ] 界面美观，操作简单
   - [ ] 支持多种筛选和排序
   - [ ] 错误提示友好
   - [ ] 支持响应式设计

4. **代码质量**
   - [ ] 测试覆盖率 > 80%
   - [ ] 代码符合项目规范
   - [ ] 文档完整清晰
   - [ ] 无安全漏洞

## 🚀 实施计划

1. **第一阶段**: 后端API开发（2天）
   - 数据模型设计
   - 业务逻辑实现
   - API接口开发

2. **第二阶段**: 前端组件开发（2天）
   - 热度分析组件
   - 图表集成
   - API集成

3. **第三阶段**: 测试和优化（1天）
   - 单元测试
   - 集成测试
   - 性能优化

4. **第四阶段**: 部署和文档（1天）
   - 功能部署
   - 用户文档
   - API文档

总计：6个工作日
