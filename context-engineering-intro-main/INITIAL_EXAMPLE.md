## FEATURE:

我想创建一个大乐透数据分析系统，该系统应该包含：

1. **数据管理模块**: 管理大乐透开奖数据
   - 开奖数据的增删改查
   - 批量数据导入和导出
   - 数据验证和清理
   - 历史数据维护

2. **数据分析模块**: 分析历史开奖数据中的模式和规律
   - 奇偶排布分析和统计
   - 分区比值计算和分布
   - 号码间隔计算和追踪
   - 和值分布分析和趋势

3. **算法统一模块**: 确保计算的一致性
   - 增量计算和普通计算使用同一套算法
   - 统一的计算入口和验证机制
   - 算法结果的一致性验证
   - 计算性能优化

4. **数据展示模块**: 简单直观的数据展示
   - 基于HTML模板的数据列表
   - 简单的统计图表展示
   - 分析结果的可视化
   - 用户友好的操作界面

该系统应该使用简化的技术栈（Python 3.13 + Flask + SQLite），采用高耦合低内聚的设计，功能集中在少数文件中，便于部署和维护。

## EXAMPLES:

在 `examples/` 文件夹中，提供了大乐透数据分析系统的代码模式和最佳实践：

在 `examples/` 文件夹中，提供了大乐透数据分析系统的代码模式和最佳实践：

- `examples/backend/lottery_app.py` - Flask应用主文件模式（高耦合设计）
- `examples/utils/calculations.py` - 统一计算算法模式（确保算法一致性）
- `examples/templates/index.html` - 简洁的页面模板模式

不要直接复制这些示例，它们是为了展示设计模式和最佳实践。请基于这些模式创建适合大乐透数据分析系统的实现。

## DOCUMENTATION:

**大乐透业务规则文档**：
- [中国福利彩票官网](http://www.cwl.gov.cn/) - 大乐透官方规则和开奖信息
- [大乐透游戏规则](http://www.cwl.gov.cn/c/2017-06-22/432575.shtml) - 详细的游戏规则说明

**技术栈文档**：
- [Flask 3.0 文档](https://flask.palletsprojects.com/) - Web框架
- [SQLAlchemy 2.0 文档](https://docs.sqlalchemy.org/) - ORM框架
- [React 18 文档](https://react.dev/) - 前端框架
- [Antd 5.0 文档](https://ant.design/) - UI组件库

**数据处理相关文档**：
- [Python 数据结构](https://docs.python.org/3/tutorial/datastructures.html) - Python内置数据结构
- [SQLite 教程](https://www.sqlite.org/quickstart.html) - SQLite数据库使用

## OTHER CONSIDERATIONS:

**大乐透业务逻辑特殊性**：
- 红球范围：1-35，每期选5个不重复号码，按升序排列
- 蓝球范围：1-12，每期选2个不重复号码，按升序排列
- 奇偶排布：基于排序后红球的奇偶性分析，如"奇偶奇奇偶"
- 分区比值：35个红球分7区，每区5个号码，如"1:1:1:1:1:0:0"
- 间隔计算：使用号码追踪算法，处理首次出现和边界情况

**技术实现注意事项**：
- 开发环境：使用Miniconda管理Python 3.13环境，简化依赖管理
- 算法统一：确保增量计算和普通计算使用完全相同的算法
- 高耦合设计：功能集中在少数文件中，降低系统复杂度
- 数据库设计：使用SQLite，简单的表结构，基础的索引优化
- 页面设计：使用Jinja2模板，简洁的HTML页面，基础的CSS样式

**算法一致性要求**：
- 统一计算入口：所有计算都通过统一的方法进行
- 结果验证机制：确保增量计算和普通计算结果完全一致
- 算法集中管理：所有计算逻辑集中在一个模块中
- 测试覆盖：重点测试算法的一致性和正确性

**用户体验考虑**：
- 界面简洁：专注于数据展示和基础操作功能
- 操作直观：简单的表单和按钮，清晰的导航结构
- 错误提示：友好的错误信息和操作指导
- 响应速度：优化数据库查询，提升页面加载速度

**项目结构要求**：
- 包含完整的README.md，说明Miniconda环境配置和使用方法
- 简单的依赖管理，只使用必要的Python包
- 单文件应用结构，便于部署和维护
- 基础的配置管理，避免复杂的环境变量配置
