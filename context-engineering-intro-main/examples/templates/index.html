<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大乐透数据分析系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e8e8e8;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .nav-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: block;
        }
        
        .nav-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            text-decoration: none;
            color: white;
        }
        
        .nav-item h3 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
        }
        
        .nav-item p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.95em;
        }
        
        .nav-item.data {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .nav-item.add {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .nav-item.analysis {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .features {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e8e8e8;
        }
        
        .features h2 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .feature-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .feature-item p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #999;
            font-size: 0.9em;
            padding-top: 20px;
            border-top: 1px solid #e8e8e8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav-menu {
                grid-template-columns: 1fr;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎱 大乐透数据分析系统</h1>
            <p>基于Python 3.13 + Flask的简单数据分析平台</p>
        </div>
        
        <div class="nav-menu">
            <a href="/data" class="nav-item data">
                <h3>📊 数据管理</h3>
                <p>查看和管理开奖数据，支持数据的增删改查操作</p>
            </a>
            
            <a href="/add" class="nav-item add">
                <h3>➕ 添加数据</h3>
                <p>录入新的开奖数据，系统自动计算分析字段</p>
            </a>
            
            <a href="/analysis" class="nav-item analysis">
                <h3>📈 数据分析</h3>
                <p>查看统计分析结果，包括奇偶排布和分区比值</p>
            </a>
        </div>
        
        <div class="features">
            <h2>系统特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🔧 算法统一</h4>
                    <p>增量计算和普通计算使用完全相同的算法，确保结果一致性</p>
                </div>
                
                <div class="feature-item">
                    <h4>🏗️ 高耦合设计</h4>
                    <p>功能集中在少数文件中，降低系统复杂度，便于维护</p>
                </div>
                
                <div class="feature-item">
                    <h4>🐍 Python 3.13</h4>
                    <p>使用最新的Python版本，通过Miniconda管理开发环境</p>
                </div>
                
                <div class="feature-item">
                    <h4>📱 简单界面</h4>
                    <p>基于Jinja2模板的简洁界面，专注于数据展示和操作</p>
                </div>
                
                <div class="feature-item">
                    <h4>🎲 业务专业</h4>
                    <p>深度理解大乐透业务规则，提供专业的数据分析功能</p>
                </div>
                
                <div class="feature-item">
                    <h4>⚡ 轻量部署</h4>
                    <p>单文件应用，SQLite数据库，部署简单，运行高效</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>大乐透数据分析系统 | 基于Context Engineering方法论开发</p>
            <p>技术栈: Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2</p>
        </div>
    </div>
</body>
</html>
