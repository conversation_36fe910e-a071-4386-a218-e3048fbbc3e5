# 大乐透数据分析系统 - 代码示例与模式

## 📋 概述

本文件夹包含大乐透数据分析系统的核心代码模式和最佳实践示例。这些示例展示了如何在我们的简化技术栈（Python 3.13 + Flask + SQLite + Jinja2）中实现各种功能。

## 🏗️ 示例结构

### 后端示例 (`backend/`)
- **Flask应用模式**: 高耦合设计的Flask应用，所有功能集中在主文件中

### 模板示例 (`templates/`)
- **页面模板模式**: 简洁的Jinja2模板设计

### 工具示例 (`utils/`)
- **计算算法模式**: 大乐透计算逻辑的统一实现，确保算法一致性

## 🎯 使用指南

每个示例都包含：
1. **代码实现**: 完整的代码示例
2. **使用说明**: 如何使用和集成
3. **最佳实践**: 设计原则和注意事项
4. **测试示例**: 相关的测试代码

## 🔄 上下文工程应用

这些示例是上下文工程的核心组成部分，AI助手会基于这些模式：
- 生成一致的代码风格
- 遵循项目架构规范
- 实现最佳实践
- 确保代码质量

## 📚 示例文件说明

### 实际示例文件

### 后端示例
- `backend/lottery_app.py` - Flask应用主文件（高耦合设计，包含所有功能）

### 模板示例
- `templates/index.html` - 主页面模板

### 工具示例
- `utils/calculations.py` - 大乐透统一计算算法

## 🎲 大乐透业务逻辑常量

```python
# 大乐透业务规则
RED_BALL_RANGE = (1, 35)  # 红球范围
BLUE_BALL_RANGE = (1, 12)  # 蓝球范围
RED_BALL_COUNT = 5        # 每期红球数量
BLUE_BALL_COUNT = 2       # 每期蓝球数量

# 分区定义：35个红球分7区
ZONES = [
    (1, 5),   # 第1区
    (6, 10),  # 第2区
    (11, 15), # 第3区
    (16, 20), # 第4区
    (21, 25), # 第5区
    (26, 30), # 第6区
    (31, 35)  # 第7区
]
```

## 🔧 技术栈配置

### 技术栈配置

### 开发环境
- **Python 3.13**: 主要开发语言
- **Miniconda**: 环境管理工具
- **Flask**: Web框架
- **SQLAlchemy**: ORM框架
- **SQLite**: 数据库
- **Jinja2**: 模板引擎

### 开发工具
- **基础测试**: 简单的功能验证
- **Git**: 版本控制
- **VS Code**: 推荐编辑器

## 📖 最佳实践原则

1. **高耦合低内聚**: 功能集中在少数文件中，减少复杂性
2. **算法统一**: 增量计算和普通计算使用同一套算法
3. **简单设计**: 避免过度抽象，保持代码简单直接
4. **环境简单**: 使用Miniconda管理Python 3.13环境
5. **基础测试**: 重点测试算法一致性和核心功能
6. **文档清晰**: 简洁明了的代码注释

## 🚀 快速开始

1. 查看相关示例文件
2. 理解代码模式和设计原则
3. 复制并修改适合你的需求
4. 运行测试确保功能正常
5. 集成到主项目中

这些示例将帮助你快速理解项目架构，并在开发新功能时保持一致的代码风格和质量标准。
