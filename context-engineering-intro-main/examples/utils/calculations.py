"""
大乐透计算算法统一模块

这个模块包含所有大乐透相关的计算算法，确保增量计算和普通计算
使用完全相同的算法，保证结果的一致性。

重要原则：
1. 所有计算逻辑集中在这个模块中
2. 增量计算和普通计算必须调用相同的方法
3. 算法实现要简单直接，避免过度抽象
4. 每个方法都要有详细的文档说明
"""

from typing import List, Dict, Any, Tuple
from collections import defaultdict

class LotteryCalculations:
    """
    大乐透计算算法统一类
    
    这个类包含所有大乐透相关的计算方法，确保算法的一致性。
    无论是增量计算还是普通计算，都必须使用这个类中的方法。
    """
    
    # 大乐透业务常量
    RED_BALL_MIN = 1
    RED_BALL_MAX = 35
    BLUE_BALL_MIN = 1
    BLUE_BALL_MAX = 12
    RED_BALL_COUNT = 5
    BLUE_BALL_COUNT = 2
    ZONE_COUNT = 7
    BALLS_PER_ZONE = 5
    
    @classmethod
    def calculate_odd_even_pattern(cls, red_balls: List[int]) -> str:
        """
        计算红球奇偶排布 - 统一算法
        
        这是核心算法之一，必须确保增量计算和普通计算
        调用这个相同的方法。
        
        Args:
            red_balls: 红球列表，可以是无序的
            
        Returns:
            奇偶排布字符串，如 "奇偶奇奇偶"
            
        Example:
            >>> calculate_odd_even_pattern([1, 8, 15, 22, 29])
            "奇偶奇偶奇"
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return ""
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return ""
        
        pattern = ""
        # 必须按升序排列后计算
        for ball in sorted(red_balls):
            pattern += "奇" if ball % 2 == 1 else "偶"
        
        return pattern
    
    @classmethod
    def calculate_zone_ratio(cls, red_balls: List[int]) -> str:
        """
        计算分区比值 - 统一算法
        
        35个红球分7区，每区5个号码：
        第1区: 1-5, 第2区: 6-10, 第3区: 11-15, 第4区: 16-20,
        第5区: 21-25, 第6区: 26-30, 第7区: 31-35
        
        Args:
            red_balls: 红球列表
            
        Returns:
            分区比值字符串，如 "1:1:1:1:1:0:0"
            
        Example:
            >>> calculate_zone_ratio([1, 8, 15, 22, 29])
            "1:1:1:1:1:0:0"
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return ""
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return ""
        
        zone_counts = [0] * cls.ZONE_COUNT
        
        for ball in red_balls:
            # 计算球所在的区间（0-6）
            zone_index = (ball - 1) // cls.BALLS_PER_ZONE
            if 0 <= zone_index < cls.ZONE_COUNT:
                zone_counts[zone_index] += 1
        
        return ":".join(map(str, zone_counts))
    
    @classmethod
    def calculate_red_ball_sum(cls, red_balls: List[int]) -> int:
        """
        计算红球和值 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            红球和值
            
        Example:
            >>> calculate_red_ball_sum([1, 8, 15, 22, 29])
            75
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return 0
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return 0
        
        return sum(red_balls)
    
    @classmethod
    def calculate_blue_ball_distance(cls, blue_balls: List[int]) -> int:
        """
        计算蓝球间距 - 统一算法
        
        Args:
            blue_balls: 蓝球列表
            
        Returns:
            蓝球间距（绝对值）
            
        Example:
            >>> calculate_blue_ball_distance([3, 7])
            4
        """
        if not blue_balls or len(blue_balls) != cls.BLUE_BALL_COUNT:
            return 0
        
        # 验证蓝球范围
        if not all(cls.BLUE_BALL_MIN <= ball <= cls.BLUE_BALL_MAX for ball in blue_balls):
            return 0
        
        return abs(blue_balls[1] - blue_balls[0])
    
    @classmethod
    def calculate_red_ball_intervals(cls, red_balls: List[int]) -> List[int]:
        """
        计算红球间隔 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            红球间隔列表
            
        Example:
            >>> calculate_red_ball_intervals([1, 8, 15, 22, 29])
            [7, 7, 7, 7]
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return []
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return []
        
        sorted_balls = sorted(red_balls)
        intervals = []
        
        for i in range(1, len(sorted_balls)):
            intervals.append(sorted_balls[i] - sorted_balls[i-1])
        
        return intervals
    
    @classmethod
    def find_last_pattern_occurrence(cls, current_pattern: str, pattern_type: str,
                                    current_period: int, historical_data: List[Dict]) -> Tuple[int, str, int]:
        """
        查找上次相同模式出现的期号、日期和间隔 - 统一算法

        Args:
            current_pattern: 当前模式值
            pattern_type: 模式类型 ('odd_even' 或 'zone_ratio')
            current_period: 当前期号
            historical_data: 历史数据列表，按期号降序排列

        Returns:
            (上次期号, 上次日期, 间隔期数)
        """
        if not current_pattern or not historical_data:
            return 0, "", 0

        # 查找上次相同模式
        for data in historical_data:
            if data['period'] >= current_period:
                continue

            if pattern_type == 'odd_even' and data.get('odd_even_pattern') == current_pattern:
                interval = current_period - data['period']
                return data['period'], data['date'], interval
            elif pattern_type == 'zone_ratio' and data.get('zone_ratio') == current_pattern:
                interval = current_period - data['period']
                return data['period'], data['date'], interval

        return 0, "", 0

    @classmethod
    def calculate_all_fields(cls, red_balls: List[int], blue_balls: List[int],
                           current_period: int = 0, historical_data: List[Dict] = None) -> Dict[str, Any]:
        """
        计算所有字段 - 统一入口方法

        这是最重要的方法，确保增量计算和普通计算都调用这个方法，
        保证算法的完全一致性。

        Args:
            red_balls: 红球列表
            blue_balls: 蓝球列表
            current_period: 当前期号（用于历史追踪）
            historical_data: 历史数据列表（用于查找上次相同模式）

        Returns:
            包含所有计算结果的字典

        Example:
            >>> calculate_all_fields([1, 8, 15, 22, 29], [3, 7], 24001, historical_data)
            {
                'odd_even_pattern': '奇偶奇偶奇',
                'zone_ratio': '1:1:1:1:1:0:0',
                'red_ball_sum': 75,
                'blue_ball_distance': 4,
                'red_ball_intervals': [7, 7, 7, 7],
                'last_odd_even_period': 23998,
                'last_odd_even_date': '2024-01-15',
                'odd_even_interval': 3,
                'last_zone_ratio_period': 23995,
                'last_zone_ratio_date': '2024-01-10',
                'zone_ratio_interval': 6
            }
        """
        # 基础计算
        result = {
            'odd_even_pattern': cls.calculate_odd_even_pattern(red_balls),
            'zone_ratio': cls.calculate_zone_ratio(red_balls),
            'red_ball_sum': cls.calculate_red_ball_sum(red_balls),
            'blue_ball_distance': cls.calculate_blue_ball_distance(blue_balls),
            'red_ball_intervals': cls.calculate_red_ball_intervals(red_balls)
        }

        # 历史追踪计算（如果提供了历史数据）
        if historical_data and current_period:
            # 查找上次相同奇偶排布
            last_odd_even_period, last_odd_even_date, odd_even_interval = cls.find_last_pattern_occurrence(
                result['odd_even_pattern'], 'odd_even', current_period, historical_data
            )
            result.update({
                'last_odd_even_period': last_odd_even_period,
                'last_odd_even_date': last_odd_even_date,
                'odd_even_interval': odd_even_interval
            })

            # 查找上次相同分区比值
            last_zone_ratio_period, last_zone_ratio_date, zone_ratio_interval = cls.find_last_pattern_occurrence(
                result['zone_ratio'], 'zone_ratio', current_period, historical_data
            )
            result.update({
                'last_zone_ratio_period': last_zone_ratio_period,
                'last_zone_ratio_date': last_zone_ratio_date,
                'zone_ratio_interval': zone_ratio_interval
            })
        else:
            # 如果没有历史数据，设置默认值
            result.update({
                'last_odd_even_period': 0,
                'last_odd_even_date': "",
                'odd_even_interval': 0,
                'last_zone_ratio_period': 0,
                'last_zone_ratio_date': "",
                'zone_ratio_interval': 0
            })

        return result
    
    @classmethod
    def validate_lottery_data(cls, red_balls: List[int], blue_balls: List[int]) -> Tuple[bool, str]:
        """
        验证开奖数据的有效性 - 统一验证算法
        
        Args:
            red_balls: 红球列表
            blue_balls: 蓝球列表
            
        Returns:
            (是否有效, 错误信息)
            
        Example:
            >>> validate_lottery_data([1, 8, 15, 22, 29], [3, 7])
            (True, "")
        """
        # 验证红球
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return False, f"红球必须有{cls.RED_BALL_COUNT}个"
        
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return False, f"红球必须在{cls.RED_BALL_MIN}-{cls.RED_BALL_MAX}范围内"
        
        if len(set(red_balls)) != cls.RED_BALL_COUNT:
            return False, "红球不能重复"
        
        # 验证蓝球
        if not blue_balls or len(blue_balls) != cls.BLUE_BALL_COUNT:
            return False, f"蓝球必须有{cls.BLUE_BALL_COUNT}个"
        
        if not all(cls.BLUE_BALL_MIN <= ball <= cls.BLUE_BALL_MAX for ball in blue_balls):
            return False, f"蓝球必须在{cls.BLUE_BALL_MIN}-{cls.BLUE_BALL_MAX}范围内"
        
        if len(set(blue_balls)) != cls.BLUE_BALL_COUNT:
            return False, "蓝球不能重复"
        
        return True, ""
    
    @classmethod
    def batch_calculate(cls, lottery_data_list: List[Dict]) -> List[Dict]:
        """
        批量计算 - 统一批量处理算法
        
        用于批量处理多条开奖数据，确保每条数据都使用相同的算法。
        
        Args:
            lottery_data_list: 开奖数据列表，每个元素包含red_balls和blue_balls
            
        Returns:
            包含计算结果的数据列表
        """
        results = []
        
        for data in lottery_data_list:
            red_balls = data.get('red_balls', [])
            blue_balls = data.get('blue_balls', [])
            
            # 验证数据
            is_valid, error_msg = cls.validate_lottery_data(red_balls, blue_balls)
            if not is_valid:
                print(f"数据验证失败: {error_msg}, 数据: {data}")
                continue
            
            # 计算所有字段
            calculated_fields = cls.calculate_all_fields(red_balls, blue_balls)
            
            # 合并原始数据和计算结果
            result = {**data, **calculated_fields}
            results.append(result)
        
        return results

# 为了保持向后兼容，提供独立的函数接口
def calculate_odd_even_pattern(red_balls: List[int]) -> str:
    """向后兼容的函数接口"""
    return LotteryCalculations.calculate_odd_even_pattern(red_balls)

def calculate_zone_ratio(red_balls: List[int]) -> str:
    """向后兼容的函数接口"""
    return LotteryCalculations.calculate_zone_ratio(red_balls)

def calculate_red_ball_sum(red_balls: List[int]) -> int:
    """向后兼容的函数接口"""
    return LotteryCalculations.calculate_red_ball_sum(red_balls)

def calculate_blue_ball_distance(blue_balls: List[int]) -> int:
    """向后兼容的函数接口"""
    return LotteryCalculations.calculate_blue_ball_distance(blue_balls)

def calculate_all_fields(red_balls: List[int], blue_balls: List[int],
                        current_period: int = 0, historical_data: List[Dict] = None) -> Dict[str, Any]:
    """向后兼容的函数接口 - 最重要的统一入口"""
    return LotteryCalculations.calculate_all_fields(red_balls, blue_balls, current_period, historical_data)

# 使用示例
if __name__ == "__main__":
    # 测试数据
    test_red_balls = [1, 8, 15, 22, 29]
    test_blue_balls = [3, 7]
    
    print("测试大乐透计算算法:")
    print(f"红球: {test_red_balls}")
    print(f"蓝球: {test_blue_balls}")
    print()
    
    # 测试所有计算方法
    result = calculate_all_fields(test_red_balls, test_blue_balls)
    for key, value in result.items():
        print(f"{key}: {value}")
    
    print()
    print("验证数据有效性:")
    is_valid, error_msg = LotteryCalculations.validate_lottery_data(test_red_balls, test_blue_balls)
    print(f"有效性: {is_valid}, 错误信息: {error_msg}")
