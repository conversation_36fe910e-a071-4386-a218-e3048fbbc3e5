"""
大乐透Flask应用主文件示例 - 简单单体应用模式

展示如何设计和实现大乐透数据分析系统的主应用文件，
采用高耦合低内聚的设计，功能集中在主文件中。

这个示例展示了：
1. 简单的Flask应用结构
2. 高耦合设计，功能集中
3. 基础的路由和视图函数
4. 统一的计算算法调用
5. 简单的错误处理
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash
from sqlalchemy import create_engine, Column, Integer, String, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from typing import List, Dict, Any

# Flask应用初始化
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# 数据库配置
DATABASE_URL = 'sqlite:///lottery.db'
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据模型定义（高耦合设计，直接在主文件中）
class LotteryData(Base):
    """大乐透开奖数据模型"""
    __tablename__ = 'lottery_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    period = Column(Integer, unique=True, nullable=False)
    date = Column(Date, nullable=False)
    red_ball_1 = Column(Integer, nullable=False)
    red_ball_2 = Column(Integer, nullable=False)
    red_ball_3 = Column(Integer, nullable=False)
    red_ball_4 = Column(Integer, nullable=False)
    red_ball_5 = Column(Integer, nullable=False)
    blue_ball_1 = Column(Integer, nullable=False)
    blue_ball_2 = Column(Integer, nullable=False)
    odd_even_pattern = Column(String(10))
    zone_ratio = Column(String(20))
    red_ball_sum = Column(Integer)
    blue_ball_distance = Column(Integer)
    # 上次相同奇偶排布追踪
    last_odd_even_period = Column(Integer)
    last_odd_even_date = Column(Date)
    odd_even_interval = Column(Integer)
    # 上次相同分区比值追踪
    last_zone_ratio_period = Column(Integer)
    last_zone_ratio_date = Column(Date)
    zone_ratio_interval = Column(Integer)

# 创建数据表
Base.metadata.create_all(bind=engine)

# 统一计算算法类（确保增量和普通计算使用同一套算法）
class LotteryCalculations:
    """
    大乐透计算算法统一类
    
    所有计算逻辑集中在这里，确保增量计算和普通计算
    使用完全相同的算法，保证结果一致性。
    """
    
    @staticmethod
    def calculate_odd_even_pattern(red_balls: List[int]) -> str:
        """
        计算奇偶排布 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            奇偶排布字符串，如 "奇偶奇奇偶"
        """
        pattern = ""
        for ball in sorted(red_balls):
            pattern += "奇" if ball % 2 == 1 else "偶"
        return pattern
    
    @staticmethod
    def calculate_zone_ratio(red_balls: List[int]) -> str:
        """
        计算分区比值 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            分区比值字符串，如 "1:1:1:1:1:0:0"
        """
        zone_counts = [0] * 7
        for ball in red_balls:
            zone_index = (ball - 1) // 5
            zone_counts[zone_index] += 1
        
        return ":".join(map(str, zone_counts))
    
    @staticmethod
    def calculate_red_ball_sum(red_balls: List[int]) -> int:
        """
        计算红球和值 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            红球和值
        """
        return sum(red_balls)
    
    @staticmethod
    def calculate_blue_ball_distance(blue_balls: List[int]) -> int:
        """
        计算蓝球间距 - 统一算法
        
        Args:
            blue_balls: 蓝球列表
            
        Returns:
            蓝球间距
        """
        if len(blue_balls) >= 2:
            return abs(blue_balls[1] - blue_balls[0])
        return 0
    
    @classmethod
    def calculate_all_fields(cls, red_balls: List[int], blue_balls: List[int]) -> Dict[str, Any]:
        """
        计算所有字段 - 统一入口
        
        确保增量计算和普通计算都调用这个方法，
        保证算法的一致性。
        
        Args:
            red_balls: 红球列表
            blue_balls: 蓝球列表
            
        Returns:
            包含所有计算结果的字典
        """
        return {
            'odd_even_pattern': cls.calculate_odd_even_pattern(red_balls),
            'zone_ratio': cls.calculate_zone_ratio(red_balls),
            'red_ball_sum': cls.calculate_red_ball_sum(red_balls),
            'blue_ball_distance': cls.calculate_blue_ball_distance(blue_balls)
        }

# 数据库操作函数（高耦合设计，直接在主文件中）
def get_db_session():
    """获取数据库会话"""
    return SessionLocal()

def insert_lottery_data(period: int, date: str, red_balls: List[int], blue_balls: List[int]) -> bool:
    """
    插入开奖数据 - 使用统一算法（包含历史追踪）

    Args:
        period: 期号
        date: 开奖日期
        red_balls: 红球列表
        blue_balls: 蓝球列表

    Returns:
        是否插入成功
    """
    try:
        session = get_db_session()

        # 获取历史数据用于模式追踪
        historical_data = []
        try:
            history_records = session.query(LotteryData).filter(
                LotteryData.period < period
            ).order_by(LotteryData.period.desc()).limit(1000).all()

            for record in history_records:
                historical_data.append({
                    'period': record.period,
                    'date': record.date.strftime('%Y-%m-%d') if record.date else "",
                    'odd_even_pattern': record.odd_even_pattern,
                    'zone_ratio': record.zone_ratio
                })
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            # 如果获取历史数据失败，继续插入但不进行历史追踪

        # 使用统一算法计算所有字段（包含历史追踪）
        calculated_fields = LotteryCalculations.calculate_all_fields(
            red_balls, blue_balls, period, historical_data
        )

        lottery_data = LotteryData(
            period=period,
            date=datetime.strptime(date, '%Y-%m-%d').date(),
            red_ball_1=red_balls[0],
            red_ball_2=red_balls[1],
            red_ball_3=red_balls[2],
            red_ball_4=red_balls[3],
            red_ball_5=red_balls[4],
            blue_ball_1=blue_balls[0],
            blue_ball_2=blue_balls[1],
            odd_even_pattern=calculated_fields['odd_even_pattern'],
            zone_ratio=calculated_fields['zone_ratio'],
            red_ball_sum=calculated_fields['red_ball_sum'],
            blue_ball_distance=calculated_fields['blue_ball_distance'],
            last_odd_even_period=calculated_fields['last_odd_even_period'],
            last_odd_even_date=datetime.strptime(calculated_fields['last_odd_even_date'], '%Y-%m-%d').date()
                if calculated_fields['last_odd_even_date'] else None,
            odd_even_interval=calculated_fields['odd_even_interval'],
            last_zone_ratio_period=calculated_fields['last_zone_ratio_period'],
            last_zone_ratio_date=datetime.strptime(calculated_fields['last_zone_ratio_date'], '%Y-%m-%d').date()
                if calculated_fields['last_zone_ratio_date'] else None,
            zone_ratio_interval=calculated_fields['zone_ratio_interval']
        )

        session.add(lottery_data)
        session.commit()
        session.close()
        return True
    except Exception as e:
        print(f"插入数据失败: {e}")
        return False

def get_all_lottery_data() -> List[Dict]:
    """获取所有开奖数据"""
    try:
        session = get_db_session()
        data = session.query(LotteryData).order_by(LotteryData.period.desc()).all()
        session.close()
        
        result = []
        for item in data:
            result.append({
                'period': item.period,
                'date': item.date,
                'red_balls': [item.red_ball_1, item.red_ball_2, item.red_ball_3,
                             item.red_ball_4, item.red_ball_5],
                'blue_balls': [item.blue_ball_1, item.blue_ball_2],
                'odd_even_pattern': item.odd_even_pattern,
                'zone_ratio': item.zone_ratio,
                'red_ball_sum': item.red_ball_sum,
                'blue_ball_distance': item.blue_ball_distance,
                # 历史追踪字段
                'last_odd_even_period': item.last_odd_even_period,
                'last_odd_even_date': item.last_odd_even_date,
                'odd_even_interval': item.odd_even_interval,
                'last_zone_ratio_period': item.last_zone_ratio_period,
                'last_zone_ratio_date': item.last_zone_ratio_date,
                'zone_ratio_interval': item.zone_ratio_interval
            })
        return result
    except Exception as e:
        print(f"查询数据失败: {e}")
        return []

def recalculate_all_data():
    """
    重新计算所有数据 - 使用统一算法（包含历史追踪）

    这个函数用于验证增量计算和普通计算的一致性
    """
    try:
        session = get_db_session()
        # 按期号升序获取所有数据，确保历史追踪的正确性
        all_data = session.query(LotteryData).order_by(LotteryData.period.asc()).all()

        # 用于存储已处理的历史数据
        processed_history = []

        for item in all_data:
            red_balls = [item.red_ball_1, item.red_ball_2, item.red_ball_3,
                        item.red_ball_4, item.red_ball_5]
            blue_balls = [item.blue_ball_1, item.blue_ball_2]

            # 使用统一算法重新计算（包含历史追踪）
            calculated_fields = LotteryCalculations.calculate_all_fields(
                red_balls, blue_balls, item.period, processed_history
            )

            # 更新数据
            item.odd_even_pattern = calculated_fields['odd_even_pattern']
            item.zone_ratio = calculated_fields['zone_ratio']
            item.red_ball_sum = calculated_fields['red_ball_sum']
            item.blue_ball_distance = calculated_fields['blue_ball_distance']
            item.last_odd_even_period = calculated_fields['last_odd_even_period']
            item.last_odd_even_date = datetime.strptime(calculated_fields['last_odd_even_date'], '%Y-%m-%d').date() \
                if calculated_fields['last_odd_even_date'] else None
            item.odd_even_interval = calculated_fields['odd_even_interval']
            item.last_zone_ratio_period = calculated_fields['last_zone_ratio_period']
            item.last_zone_ratio_date = datetime.strptime(calculated_fields['last_zone_ratio_date'], '%Y-%m-%d').date() \
                if calculated_fields['last_zone_ratio_date'] else None
            item.zone_ratio_interval = calculated_fields['zone_ratio_interval']

            # 将当前数据添加到历史记录中，供后续数据使用
            processed_history.append({
                'period': item.period,
                'date': item.date.strftime('%Y-%m-%d') if item.date else "",
                'odd_even_pattern': calculated_fields['odd_even_pattern'],
                'zone_ratio': calculated_fields['zone_ratio']
            })

        session.commit()
        session.close()
        return True
    except Exception as e:
        print(f"重新计算失败: {e}")
        return False

# Flask路由定义（高耦合设计，所有路由在主文件中）
@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/data')
def data_list():
    """数据列表页"""
    data = get_all_lottery_data()
    return render_template('data_list.html', data=data)

@app.route('/add', methods=['GET', 'POST'])
def add_data():
    """添加数据页"""
    if request.method == 'POST':
        try:
            period = int(request.form['period'])
            date = request.form['date']
            red_balls = [
                int(request.form['red_ball_1']),
                int(request.form['red_ball_2']),
                int(request.form['red_ball_3']),
                int(request.form['red_ball_4']),
                int(request.form['red_ball_5'])
            ]
            blue_balls = [
                int(request.form['blue_ball_1']),
                int(request.form['blue_ball_2'])
            ]
            
            # 数据验证
            if not all(1 <= ball <= 35 for ball in red_balls):
                flash('红球必须在1-35范围内')
                return render_template('add_data.html')
            
            if not all(1 <= ball <= 12 for ball in blue_balls):
                flash('蓝球必须在1-12范围内')
                return render_template('add_data.html')
            
            if len(set(red_balls)) != 5:
                flash('红球不能重复')
                return render_template('add_data.html')
            
            if len(set(blue_balls)) != 2:
                flash('蓝球不能重复')
                return render_template('add_data.html')
            
            # 插入数据（使用统一算法）
            if insert_lottery_data(period, date, red_balls, blue_balls):
                flash('数据添加成功')
                return redirect(url_for('data_list'))
            else:
                flash('数据添加失败')
        
        except ValueError:
            flash('请输入有效的数字')
        except Exception as e:
            flash(f'添加失败: {str(e)}')
    
    return render_template('add_data.html')

@app.route('/analysis')
def analysis():
    """分析页面"""
    data = get_all_lottery_data()
    
    # 简单的统计分析
    if data:
        total_count = len(data)
        avg_red_sum = sum(item['red_ball_sum'] for item in data) / total_count
        
        # 奇偶排布统计
        odd_even_stats = {}
        for item in data:
            pattern = item['odd_even_pattern']
            odd_even_stats[pattern] = odd_even_stats.get(pattern, 0) + 1
        
        analysis_result = {
            'total_count': total_count,
            'avg_red_sum': round(avg_red_sum, 2),
            'odd_even_stats': odd_even_stats
        }
    else:
        analysis_result = {}
    
    return render_template('analysis.html', analysis=analysis_result)

@app.route('/recalculate')
def recalculate():
    """重新计算所有数据"""
    if recalculate_all_data():
        flash('重新计算完成')
    else:
        flash('重新计算失败')
    return redirect(url_for('data_list'))

if __name__ == '__main__':
    # 从环境变量获取配置，或使用默认值
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    # 启动应用
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True  # 支持多线程
    )
