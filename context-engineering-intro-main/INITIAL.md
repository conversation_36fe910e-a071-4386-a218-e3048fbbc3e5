## FEATURE:

基于我们现有的大乐透数据分析系统，实现数据分析功能的上下文工程优化。系统采用简化的技术栈：Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2，包含以下核心功能：

- **数据管理**：开奖数据的增删改查、批量删除、数据更新
- **数据分析**：奇偶排布、分区比值、红球间隔、蓝球间距分析
- **数据展示**：简单的HTML页面展示分析结果
- **系统管理**：基础的数据库操作和文件管理

## EXAMPLES:

在 `examples/` 文件夹中，提供以下大乐透数据分析系统的代码模式和最佳实践：

- `examples/backend/` - Flask后端模式
  - `lottery_app.py` - Flask应用主文件（高耦合设计，包含所有功能）

- `examples/templates/` - HTML模板模式
  - `index.html` - 主页面模板

- `examples/utils/` - 工具函数模式
  - `calculations.py` - 大乐透统一计算算法

- `examples/README.md` - 示例说明文档

## DOCUMENTATION:

**核心技术文档**：
- Python 3.13 文档: https://docs.python.org/3.13/
- Flask 文档: https://flask.palletsprojects.com/
- SQLAlchemy 文档: https://docs.sqlalchemy.org/
- Miniconda 安装: https://docs.conda.io/en/latest/miniconda.html

## OTHER CONSIDERATIONS:

**大乐透业务逻辑特殊性**：
- 红球范围：1-35，每期选5个不重复号码
- 蓝球范围：1-12，每期选2个不重复号码
- 奇偶排布：基于排序后红球的奇偶性分析
- 分区比值：35个红球分7区，每区5个号码
- 间隔计算：使用号码追踪算法，处理边界情况

**技术实现注意事项**：
- 开发环境：使用Miniconda管理Python 3.13环境
- 代码组织：高耦合低内聚，功能集中在少数文件中
- 算法统一：增量计算和普通计算使用同一套算法
- 简单部署：单文件应用，易于部署和维护

**数据计算要求**：
- 统一算法：确保增量更新和全量计算结果一致
- 计算集中：所有计算逻辑集中在核心模块中
- 数据一致性：保证不同计算方式的结果完全相同
- 历史追踪：计算上次相同奇偶排布和分区比值的出现日期和间隔

**上下文工程应用场景**：
- 新功能开发：使用PRP生成详细实现方案
- 代码重构：基于现有模式进行架构优化
- 算法优化：确保计算逻辑的一致性和正确性
- 测试用例生成：基于业务逻辑生成全面测试
- 文档生成：API文档、用户手册、技术文档

## 文件目录设计

### 项目整体结构
```
lottery-analysis-system/
├── app.py                      # Flask应用主文件（高耦合设计，包含所有功能）
├── calculations.py             # 统一计算算法模块
├── requirements.txt            # Python依赖包
├── README.md                   # 项目说明文档
├── lottery.db                  # SQLite数据库文件
├── templates/                  # Jinja2模板目录
│   ├── base.html              # 基础模板
│   ├── index.html             # 主页模板
│   ├── data_list.html         # 数据列表模板
│   ├── add_data.html          # 添加数据模板
│   └── analysis.html          # 分析结果模板
├── static/                     # 静态文件目录
│   ├── css/
│   │   └── style.css          # 样式文件
│   └── js/
│       └── main.js            # JavaScript文件
├── logs/                       # 日志文件目录
│   ├── app.log                # 应用日志
│   └── error.log              # 错误日志
└── tests/                      # 测试文件目录
    ├── test_calculations.py   # 算法一致性测试
    └── test_app.py            # 应用功能测试
```

### 核心文件说明

#### 1. app.py - Flask应用主文件
```python
"""
Flask应用主文件 - 高耦合设计
包含所有路由、视图函数和核心业务逻辑
"""
# 主要内容：
# - Flask应用初始化
# - 数据库配置
# - 所有路由定义
# - 视图函数实现
# - 错误处理
# - 应用启动
```

#### 2. calculations.py - 统一计算算法模块
```python
"""
大乐透计算算法统一模块
确保增量计算和普通计算使用同一套算法
"""
# 主要内容：
# - LotteryCalculations类
# - 奇偶排布计算
# - 分区比值计算
# - 红球和值计算
# - 蓝球间距计算
# - 上次相同模式出现日期和间隔计算
# - 数据验证方法
# - 批量计算方法
```

#### 3. index.html - 主页面模板
```html
<!--
系统主页模板，包含：
- 系统介绍和导航
- 功能入口链接
- 简洁的页面布局
- 响应式设计
-->
```

### 模板文件结构

#### templates/base.html - 基础模板
```html
<!--
基础HTML模板，包含：
- 页面头部和导航
- 公共CSS和JS引用
- 页面布局框架
- 消息提示区域
-->
```

#### templates/index.html - 主页模板
```html
<!--
系统主页，包含：
- 系统介绍
- 功能导航菜单
- 统计概览
- 快速操作入口
-->
```

#### templates/data_list.html - 数据列表模板
```html
<!--
数据列表页面，包含：
- 开奖数据表格
- 分页导航
- 搜索和筛选
- 批量操作按钮
-->
```

#### templates/add_data.html - 添加数据模板
```html
<!--
数据添加页面，包含：
- 数据输入表单
- 数据验证提示
- 提交和重置按钮
- 输入格式说明
-->
```

#### templates/analysis.html - 分析结果模板
```html
<!--
分析结果页面，包含：
- 统计图表展示
- 分析数据表格
- 趋势分析结果
- 导出功能
-->
```

### 静态文件结构

#### static/css/style.css - 样式文件
```css
/*
主要样式定义：
- 页面布局样式
- 表格和表单样式
- 响应式设计
- 主题色彩配置
*/
```

#### static/js/main.js - JavaScript文件
```javascript
// 主要功能：
// - 表单验证
// - 数据交互
// - 页面动态效果
// - AJAX请求处理
```

### 测试文件结构

#### tests/test_calculations.py - 算法一致性测试
```python
"""
重点测试算法的一致性和正确性
"""
# 测试内容：
# - 奇偶排布计算测试
# - 分区比值计算测试
# - 增量vs普通计算一致性测试
# - 边界条件测试
# - 性能测试
```

#### tests/test_database.py - 数据库操作测试
```python
"""
数据库操作功能测试
"""
# 测试内容：
# - 数据插入测试
# - 数据查询测试
# - 数据更新测试
# - 数据删除测试
# - 批量操作测试
```

#### tests/test_app.py - 应用功能测试
```python
"""
Flask应用功能测试
"""
# 测试内容：
# - 路由访问测试
# - 表单提交测试
# - 页面渲染测试
# - 错误处理测试
# - 用户交互测试
```

### 配置文件说明

#### config.py - 配置文件
```python
"""
应用配置管理
"""
# 配置内容：
# - 数据库配置
# - Flask应用配置
# - 日志配置
# - 环境变量配置
```

#### requirements.txt - 依赖包
```
# Python依赖包列表
Flask==2.3.3
SQLAlchemy==2.0.23
Jinja2==3.1.2
# 其他必要依赖...
```

### 文档目录结构

#### docs/api.md - API文档
```markdown
# API接口文档
- 路由说明
- 请求参数
- 响应格式
- 错误代码
- 使用示例
```

#### docs/deployment.md - 部署说明
```markdown
# 部署指南
- 环境要求
- 安装步骤
- 配置说明
- 启动方式
- 故障排除
```

#### docs/algorithm.md - 算法说明
```markdown
# 算法文档
- 计算逻辑说明
- 算法实现细节
- 一致性保证机制
- 性能优化说明
- 测试验证方法
```

### 环境配置和运行

#### Miniconda环境配置
```bash
# 创建Python 3.13环境
conda create -n lottery-analysis python=3.13

# 激活环境
conda activate lottery-analysis

# 安装依赖
pip install -r requirements.txt
```

#### 应用目录结构
```
lottery-analysis-system/
├── app.py                     # 主应用文件（高耦合设计，包含所有功能）
├── calculations.py            # 统一计算算法模块
├── requirements.txt           # 依赖包
├── templates/                 # 模板文件
├── static/                    # 静态文件
├── lottery.db                 # 数据库文件
└── logs/                      # 日志文件
    ├── app.log               # 应用日志
    └── error.log             # 错误日志
```

#### 启动应用
```bash
# 激活环境
conda activate lottery-analysis

# 切换到应用目录
cd lottery-analysis-system

# 启动应用
python app.py

# 或指定主机和端口
python app.py --host=0.0.0.0 --port=5000
```

#### app.py配置示例
```python
import os
from flask import Flask

app = Flask(__name__)

# 应用配置
if __name__ == '__main__':
    # 从环境变量获取配置，或使用默认值
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    # 启动应用
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True  # 支持多线程
    )
```

#### 环境变量配置
```bash
# 可选：设置环境变量
export FLASK_HOST=0.0.0.0
export FLASK_PORT=5000
export FLASK_DEBUG=False
export DATABASE_URL=sqlite:///lottery.db

# 启动应用
python app.py
```



#### 日志管理
```python
# 在app.py中添加日志配置
import logging
from logging.handlers import RotatingFileHandler
import os

# 确保logs目录存在
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置日志文件
file_handler = RotatingFileHandler(
    'logs/app.log',
    maxBytes=10240000,  # 10MB
    backupCount=5
)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.INFO)
app.logger.addHandler(file_handler)
app.logger.setLevel(logging.INFO)
app.logger.info('Lottery Analysis System startup')
```

#### 应用管理
```bash
# 检查应用状态
curl http://localhost:5000/

# 查看日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 停止应用
# 在运行应用的终端按Ctrl+C

# 重启应用
python app.py
```
