# 大乐透数据分析系统 - Context Engineering Template

基于Context Engineering方法论的大乐透开奖数据分析与AI预测系统。本项目展示了如何在数据分析和机器学习项目中应用上下文工程技术，实现高质量的AI辅助开发。

> **Context Engineering在数据分析项目中的威力：让AI助手真正理解业务逻辑，生成符合领域特点的高质量代码。**

## 🎯 项目概述

### 技术栈
- **后端**: Python 3.13 + Flask + SQLAlchemy + SQLite
- **前端**: Jinja2模板 + HTML + CSS
- **开发环境**: Miniconda
- **开发工具**: Context Engineering + Claude Code

### 核心功能
- **数据管理**: 开奖数据的增删改查、批量操作、数据更新
- **数据分析**: 奇偶排布、分区比值、红球间隔、历史模式分析
- **数据展示**: 简单的HTML页面展示分析结果
- **算法统一**: 增量计算和普通计算使用同一套算法

## 🚀 Context Engineering 快速开始

### 1. 环境准备
```bash
# 安装Miniconda (如果未安装)
# 下载地址: https://docs.conda.io/en/latest/miniconda.html

# 创建Python 3.13环境
conda create -n lottery-analysis python=3.13
conda activate lottery-analysis

# 克隆项目
git clone <repository-url>
cd lottery-analysis-system

# 安装依赖
pip install flask sqlalchemy
```

### 2. 上下文工程设置
```bash
# 1. 查看项目规则和约定
cat CLAUDE.md

# 2. 了解代码示例和模式
ls examples/

# 3. 创建功能需求
cp INITIAL.md my-feature.md
# 编辑 my-feature.md 描述你的需求

# 4. 在Claude Code中生成PRP
/generate-prp my-feature.md

# 5. 执行PRP实现功能
/execute-prp PRPs/my-feature-name.md
```

### 3. 启动系统
```bash
# 激活环境
conda activate lottery-analysis

# 启动Flask应用
python app.py

# 访问应用
# 浏览器打开 http://localhost:5000
```

## 📚 Table of Contents

- [What is Context Engineering?](#what-is-context-engineering)
- [Template Structure](#template-structure)
- [Step-by-Step Guide](#step-by-step-guide)
- [Writing Effective INITIAL.md Files](#writing-effective-initialmd-files)
- [The PRP Workflow](#the-prp-workflow)
- [Using Examples Effectively](#using-examples-effectively)
- [Best Practices](#best-practices)

## 📚 Context Engineering 在数据分析中的应用

### 业务领域上下文
我们的系统专注于大乐透数据分析，包含特定的业务规则：

```python
# 大乐透业务规则示例
RED_BALL_RANGE = (1, 35)  # 红球范围
BLUE_BALL_RANGE = (1, 12)  # 蓝球范围
RED_BALL_COUNT = 5        # 每期红球数量
BLUE_BALL_COUNT = 2       # 每期蓝球数量

# 分区定义：35个红球分7区
ZONES = [
    (1, 5),   # 第1区
    (6, 10),  # 第2区
    (11, 15), # 第3区
    (16, 20), # 第4区
    (21, 25), # 第5区
    (26, 30), # 第6区
    (31, 35)  # 第7区
]
```

### 代码模式上下文
在 `examples/` 文件夹中，我们提供了完整的代码模式：

- **应用主文件模式**: Flask应用的组织结构
- **数据模型模式**: SQLAlchemy模型设计
- **计算算法模式**: 大乐透计算逻辑
- **模板设计模式**: Jinja2模板使用
- **测试模式**: 基础功能测试

### Context Engineering vs 传统开发

**传统Prompt Engineering:**
- 依赖巧妙的措辞和特定短语
- 局限于如何表达任务
- 像给某人一张便签

**Context Engineering:**
- 提供全面上下文的完整系统
- 包含文档、示例、规则、模式和验证
- 像编写包含所有细节的完整剧本

### 为什么Context Engineering在数据分析中重要

1. **减少AI失败**: 大多数AI失败不是模型问题，而是上下文问题
2. **确保一致性**: AI遵循项目模式和约定
3. **支持复杂功能**: AI能处理多步骤实现
4. **自我纠错**: 验证循环允许AI修复自己的错误
5. **算法一致性**: 确保增量和普通计算使用同一套算法

## 🏗️ 项目结构

```
lottery-analysis-context-engineering/
├── .claude/
│   ├── commands/
│   │   ├── generate-prp.md    # 生成综合PRP
│   │   └── execute-prp.md     # 执行PRP实现功能
│   └── settings.local.json    # Claude Code权限设置
├── PRPs/
│   ├── templates/
│   │   └── prp_base.md       # PRP基础模板
│   └── EXAMPLE_lottery_analysis_prp.md  # 大乐透分析PRP示例
├── examples/                  # 代码示例（关键！）
│   ├── backend/              # 后端示例
│   │   └── lottery_app.py           # Flask应用主文件模式（高耦合设计）
│   ├── templates/            # 模板示例
│   │   └── index.html               # 主页面模板
│   ├── utils/                # 工具示例
│   │   └── calculations.py          # 统一计算算法模式
│   └── README.md             # 示例说明文档
├── CLAUDE.md                 # AI助手全局规则
├── INITIAL.md               # 功能需求模板
├── INITIAL_EXAMPLE.md       # 需求示例
└── README.md                # 本文件
```

这个模板专注于数据分析和AI预测领域的上下文工程应用。

## Step-by-Step Guide

### 1. Set Up Global Rules (CLAUDE.md)

The `CLAUDE.md` file contains project-wide rules that the AI assistant will follow in every conversation. The template includes:

- **Project awareness**: Reading planning docs, checking tasks
- **Code structure**: File size limits, module organization
- **Testing requirements**: Unit test patterns, coverage expectations
- **Style conventions**: Language preferences, formatting rules
- **Documentation standards**: Docstring formats, commenting practices

**You can use the provided template as-is or customize it for your project.**

### 2. Create Your Initial Feature Request

Edit `INITIAL.md` to describe what you want to build:

```markdown
## FEATURE:
[Describe what you want to build - be specific about functionality and requirements]

## EXAMPLES:
[List any example files in the examples/ folder and explain how they should be used]

## DOCUMENTATION:
[Include links to relevant documentation, APIs, or MCP server resources]

## OTHER CONSIDERATIONS:
[Mention any gotchas, specific requirements, or things AI assistants commonly miss]
```

**See `INITIAL_EXAMPLE.md` for a complete example.**

### 3. Generate the PRP

PRPs (Product Requirements Prompts) are comprehensive implementation blueprints that include:

- Complete context and documentation
- Implementation steps with validation
- Error handling patterns
- Test requirements

They are similar to PRDs (Product Requirements Documents) but are crafted more specifically to instruct an AI coding assistant.

Run in Claude Code:
```bash
/generate-prp INITIAL.md
```

**Note:** The slash commands are custom commands defined in `.claude/commands/`. You can view their implementation:
- `.claude/commands/generate-prp.md` - See how it researches and creates PRPs
- `.claude/commands/execute-prp.md` - See how it implements features from PRPs

The `$ARGUMENTS` variable in these commands receives whatever you pass after the command name (e.g., `INITIAL.md` or `PRPs/your-feature.md`).

This command will:
1. Read your feature request
2. Research the codebase for patterns
3. Search for relevant documentation
4. Create a comprehensive PRP in `PRPs/your-feature-name.md`

### 4. Execute the PRP

Once generated, execute the PRP to implement your feature:

```bash
/execute-prp PRPs/your-feature-name.md
```

The AI coding assistant will:
1. Read all context from the PRP
2. Create a detailed implementation plan
3. Execute each step with validation
4. Run tests and fix any issues
5. Ensure all success criteria are met

## Writing Effective INITIAL.md Files

### Key Sections Explained

**FEATURE**: Be specific and comprehensive
- ❌ "Build a web scraper"
- ✅ "Build an async web scraper using BeautifulSoup that extracts product data from e-commerce sites, handles rate limiting, and stores results in PostgreSQL"

**EXAMPLES**: Leverage the examples/ folder
- Place relevant code patterns in `examples/`
- Reference specific files and patterns to follow
- Explain what aspects should be mimicked

**DOCUMENTATION**: Include all relevant resources
- API documentation URLs
- Library guides
- MCP server documentation
- Database schemas

**OTHER CONSIDERATIONS**: Capture important details
- Authentication requirements
- Rate limits or quotas
- Common pitfalls
- Performance requirements

## The PRP Workflow

### How /generate-prp Works

The command follows this process:

1. **Research Phase**
   - Analyzes your codebase for patterns
   - Searches for similar implementations
   - Identifies conventions to follow

2. **Documentation Gathering**
   - Fetches relevant API docs
   - Includes library documentation
   - Adds gotchas and quirks

3. **Blueprint Creation**
   - Creates step-by-step implementation plan
   - Includes validation gates
   - Adds test requirements

4. **Quality Check**
   - Scores confidence level (1-10)
   - Ensures all context is included

### How /execute-prp Works

1. **Load Context**: Reads the entire PRP
2. **Plan**: Creates detailed task list using TodoWrite
3. **Execute**: Implements each component
4. **Validate**: Runs tests and linting
5. **Iterate**: Fixes any issues found
6. **Complete**: Ensures all requirements met

See `PRPs/EXAMPLE_multi_agent_prp.md` for a complete example of what gets generated.

## Using Examples Effectively

The `examples/` folder is **critical** for success. AI coding assistants perform much better when they can see patterns to follow.

### What to Include in Examples

1. **Code Structure Patterns**
   - How you organize modules
   - Import conventions
   - Class/function patterns

2. **Testing Patterns**
   - Test file structure
   - Mocking approaches
   - Assertion styles

3. **Integration Patterns**
   - API client implementations
   - Database connections
   - Authentication flows

4. **CLI Patterns**
   - Argument parsing
   - Output formatting
   - Error handling

### Example Structure

```
examples/
├── README.md           # Explains what each example demonstrates
├── cli.py             # CLI implementation pattern
├── agent/             # Agent architecture patterns
│   ├── agent.py      # Agent creation pattern
│   ├── tools.py      # Tool implementation pattern
│   └── providers.py  # Multi-provider pattern
└── tests/            # Testing patterns
    ├── test_agent.py # Unit test patterns
    └── conftest.py   # Pytest configuration
```

## Best Practices

### 1. Be Explicit in INITIAL.md
- Don't assume the AI knows your preferences
- Include specific requirements and constraints
- Reference examples liberally

### 2. Provide Comprehensive Examples
- More examples = better implementations
- Show both what to do AND what not to do
- Include error handling patterns

### 3. Use Validation Gates
- PRPs include test commands that must pass
- AI will iterate until all validations succeed
- This ensures working code on first try

### 4. Leverage Documentation
- Include official API docs
- Add MCP server resources
- Reference specific documentation sections

### 5. Customize CLAUDE.md
- Add your conventions
- Include project-specific rules
- Define coding standards

## 🎲 大乐透业务逻辑示例

### 奇偶排布分析
```python
def calculate_odd_even_pattern(red_balls: List[int]) -> str:
    """
    计算红球奇偶排布

    Args:
        red_balls: 排序后的红球列表

    Returns:
        奇偶排布字符串，如 "奇偶奇奇偶"
    """
    pattern = ""
    for ball in sorted(red_balls):
        pattern += "奇" if ball % 2 == 1 else "偶"
    return pattern
```

### 分区比值计算
```python
def calculate_zone_ratio(red_balls: List[int]) -> str:
    """
    计算分区比值

    Args:
        red_balls: 红球列表

    Returns:
        分区比值字符串，如 "1:1:1:1:1:0:0"
    """
    zone_counts = [0] * 7
    for ball in red_balls:
        zone_index = (ball - 1) // 5
        zone_counts[zone_index] += 1

    return ":".join(map(str, zone_counts))
```

## 🔧 算法一致性保证

### 统一计算模块
```python
class LotteryCalculations:
    """
    大乐透计算算法统一模块

    确保增量计算和普通计算使用同一套算法
    """

    @staticmethod
    def calculate_odd_even_pattern(red_balls: List[int]) -> str:
        """
        计算奇偶排布 - 统一算法

        Args:
            red_balls: 红球列表

        Returns:
            奇偶排布字符串
        """
        pattern = ""
        for ball in sorted(red_balls):
            pattern += "奇" if ball % 2 == 1 else "偶"
        return pattern

    @staticmethod
    def calculate_zone_ratio(red_balls: List[int]) -> str:
        """
        计算分区比值 - 统一算法

        Args:
            red_balls: 红球列表

        Returns:
            分区比值字符串
        """
        zone_counts = [0] * 7
        for ball in red_balls:
            zone_index = (ball - 1) // 5
            zone_counts[zone_index] += 1

        return ":".join(map(str, zone_counts))
```

## 📊 简单数据库设计

### 基础表结构
```sql
-- 开奖数据主表
CREATE TABLE lottery_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period INTEGER UNIQUE NOT NULL,
    date DATE NOT NULL,
    red_ball_1 INTEGER NOT NULL,
    red_ball_2 INTEGER NOT NULL,
    red_ball_3 INTEGER NOT NULL,
    red_ball_4 INTEGER NOT NULL,
    red_ball_5 INTEGER NOT NULL,
    blue_ball_1 INTEGER NOT NULL,
    blue_ball_2 INTEGER NOT NULL,
    odd_even_pattern TEXT,
    zone_ratio TEXT,
    red_ball_sum INTEGER,
    blue_ball_distance INTEGER,
    -- 上次相同奇偶排布追踪
    last_odd_even_period INTEGER,
    last_odd_even_date DATE,
    odd_even_interval INTEGER,
    -- 上次相同分区比值追踪
    last_zone_ratio_period INTEGER,
    last_zone_ratio_date DATE,
    zone_ratio_interval INTEGER
);

-- 基础索引
CREATE INDEX idx_lottery_period ON lottery_data(period);
CREATE INDEX idx_lottery_date ON lottery_data(date);
-- 模式查询索引
CREATE INDEX idx_odd_even_pattern ON lottery_data(odd_even_pattern);
CREATE INDEX idx_zone_ratio ON lottery_data(zone_ratio);
```

### 简单页面展示
```html
<!-- 基础的数据展示模板 -->
<table>
  <thead>
    <tr>
      <th>期号</th>
      <th>红球</th>
      <th>蓝球</th>
      <th>奇偶排布</th>
    </tr>
  </thead>
  <tbody>
    {% for item in data %}
    <tr>
      <td>{{ item.period }}</td>
      <td>{{ item.red_balls }}</td>
      <td>{{ item.blue_balls }}</td>
      <td>{{ item.odd_even_pattern }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
```

## 🧪 测试策略

### 算法一致性测试
```python
def test_odd_even_pattern_calculation():
    """测试奇偶排布计算 - 确保算法一致性"""
    # 测试用例：红球 [1, 8, 15, 22, 29]
    red_balls = [1, 8, 15, 22, 29]
    expected = "奇偶奇偶奇"

    # 测试普通计算
    result1 = LotteryCalculations.calculate_odd_even_pattern(red_balls)
    # 测试增量计算（应该使用同一个方法）
    result2 = LotteryCalculations.calculate_odd_even_pattern(red_balls)

    assert result1 == expected
    assert result1 == result2  # 确保结果一致
```

### 基础功能测试
```python
def test_lottery_data_crud():
    """测试基础的增删改查功能"""
    # 测试数据插入 - 使用实际的函数接口
    period = 24001
    date = '2024-01-20'
    red_balls = [1, 8, 15, 22, 29]
    blue_balls = [3, 7]

    # 插入数据
    result = insert_lottery_data(period, date, red_balls, blue_balls)
    assert result is True

    # 查询所有数据
    all_data = get_all_lottery_data()
    assert len(all_data) > 0

    # 验证插入的数据
    inserted_data = next((item for item in all_data if item['period'] == 24001), None)
    assert inserted_data is not None
    assert inserted_data['red_balls'] == red_balls
    assert inserted_data['blue_balls'] == blue_balls
```

## 📖 最佳实践

### 1. 上下文工程原则
- **业务优先**: 深入理解大乐透业务逻辑
- **算法统一**: 确保增量和普通计算使用同一套算法
- **简单设计**: 高耦合低内聚，功能集中
- **环境简单**: 使用Miniconda管理Python 3.13环境

### 2. 代码质量标准
- **类型注解**: 使用Python type hints
- **文档字符串**: Google风格的docstring
- **算法一致性**: 重点测试计算逻辑的一致性
- **基础测试**: 确保核心功能正确

### 3. AI助手协作
- **明确需求**: 在INITIAL.md中详细描述功能
- **提供上下文**: 引用相关的代码示例
- **算法验证**: 重点验证计算算法的一致性
- **知识积累**: 将解决方案加入examples

## 🔗 相关资源

- [Context Engineering 官方文档](https://github.com/coleam00/context-engineering-intro)
- [Claude Code 使用指南](https://docs.anthropic.com/en/docs/claude-code)
- [大乐透官方规则](http://www.cwl.gov.cn/)
- [Flask 官方文档](https://flask.palletsprojects.com/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)
- [Miniconda 安装指南](https://docs.conda.io/en/latest/miniconda.html)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件