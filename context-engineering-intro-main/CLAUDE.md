### 🎯 大乐透数据分析项目上下文

- **项目性质**: 大乐透开奖数据分析系统
- **技术栈**: Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2
- **核心业务**: 开奖数据管理、模式分析、数据展示
- **架构模式**: 简单单体应用、高耦合低内聚、功能集中

### 🔄 项目感知与上下文工程

- **始终阅读项目文档**: 在新对话开始时阅读项目文档
- **检查任务状态**: 查看 `TASK.md` 了解当前开发任务和优先级
- **遵循代码模式**: 使用 `examples/` 中的简单代码模式
- **应用上下文工程**: 为每个功能创建详细的PRP（产品需求提示）
- **环境要求**: 使用Miniconda管理Python 3.13环境

### 🏗️ 大乐透应用架构规范

- **主应用文件** (`app.py`):
  - Flask应用主文件，包含所有路由和核心逻辑
  - 高耦合设计，功能集中在主文件中

- **数据模型** (`models.py`):
  - `LotteryData`: 开奖数据主表
  - 简单的SQLAlchemy模型定义

- **计算模块** (`calculations.py`):
  - 所有大乐透计算算法集中在此文件
  - 确保增量计算和普通计算使用同一套算法
  - 高耦合设计，所有计算逻辑在一个模块中

- **模板文件** (`templates/`):
  - 简单的HTML模板，使用Jinja2渲染
  - 基础的数据展示页面

### 🎲 大乐透业务逻辑规范

- **红球规则**: 1-35范围，每期5个不重复号码，按升序排列
- **蓝球规则**: 1-12范围，每期2个不重复号码，按升序排列
- **奇偶排布**: 基于排序后红球计算，格式如"奇偶奇奇偶"
- **分区比值**: 35个红球分7区(1-5,6-10,11-15,16-20,21-25,26-30,31-35)
- **间隔计算**: 使用号码追踪算法，处理首次出现和边界情况

### 🧱 代码结构与组织

- **高耦合低内聚**: 功能集中在少数文件中，减少文件数量
- **算法统一**: 增量计算和普通计算必须使用同一套算法
- **简单结构**: 避免过度抽象，保持代码简单直接
- **命名约定**:
  - 类名: `PascalCase` (如 `LotteryData`)
  - 函数/变量: `snake_case` (如 `calculate_intervals`)
  - 常量: `UPPER_SNAKE_CASE` (如 `MAX_RED_BALL`)

### 🧪 测试与质量保证

- **算法一致性测试**: 重点测试增量计算和普通计算结果的一致性
- **简单测试**: 基础的功能测试，确保核心计算正确
- **测试类型**:
  - 计算逻辑测试: 验证大乐透计算算法的正确性
  - 数据一致性测试: 确保不同计算方式结果相同
  - 基础功能测试: 验证数据增删改查功能
- **测试数据**: 使用真实的大乐透历史数据进行测试

### ✅ 任务完成标准

- **标记完成任务**: 在 `TASK.md` 中立即标记已完成的任务
- 在开发过程中发现的新子任务或TODO添加到 `TASK.md` 的"开发中发现"部分

### 📎 代码风格与约定

- **主要语言**: Python 3.13
- **代码规范**: 遵循PEP8，使用type hints
- **开发环境**: 使用Miniconda管理Python环境
- **框架**: 使用 `Flask` 构建Web应用，使用 `SQLAlchemy` ORM
- **文档字符串**: 每个函数都使用Google风格编写docstring:
  ```python
  def example():
      """
      简要说明。

      Args:
          param1 (type): 描述。

      Returns:
          type: 描述。
      """
  ```

### 🎨 前端开发规范

- **模板引擎**: 使用Jinja2模板引擎
- **页面设计**: 简单的HTML页面，基础的CSS样式
- **数据展示**: 使用HTML表格展示数据
- **用户交互**: 基础的表单提交和页面跳转

### 📚 文档与可解释性

- **更新README.md**: 当添加新功能、依赖变更或设置步骤修改时
- **注释非显而易见的代码**: 确保中级开发者能够理解所有内容
- 编写复杂逻辑时，**添加内联 `# 原因:` 注释**解释为什么，而不仅仅是做什么

### 🧠 AI助手行为规则

- **永远不要假设缺失的上下文。如果不确定就提问。**
- **永远不要虚构库或函数** – 只使用已知的、经过验证的Python包
- **始终确认文件路径和模块名称**在代码或测试中引用之前确实存在
- **永远不要删除或覆盖现有代码**，除非明确指示或作为 `TASK.md` 中任务的一部分

### 🔧 开发工具与环境

- **Python环境**: 使用Miniconda管理Python 3.13环境
- **依赖管理**: 使用conda和pip管理依赖包
- **代码格式**: Python使用基础的PEP8规范
- **类型检查**: Python使用type hints
- **版本控制**: Git工作流，简单的分支管理

### 📊 上下文工程应用

- **PRP创建**: 为每个新功能创建详细的产品需求提示
- **代码示例**: 在 `examples/` 中维护最新的代码模式
- **文档更新**: 及时更新API文档、用户手册、技术文档
- **知识传承**: 将解决方案和最佳实践记录在上下文中

### 🚨 大乐透特定注意事项

- **业务理解**: 深入理解大乐透业务逻辑和数据特点
- **数据完整性**: 确保开奖数据的准确性和一致性
- **算法一致性**: 确保增量计算和普通计算使用同一套算法
- **计算正确性**: 重点关注计算逻辑的正确性和一致性
- **简单部署**: 保持应用简单，易于部署和维护
