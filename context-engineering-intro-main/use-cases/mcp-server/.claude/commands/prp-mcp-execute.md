---
name: "prp-mcp-execute"
description: This command is designed to create a comprehensive Model Context Protocol (MCP) servers following the specific Product Requirement Prompt (PRP) passed as an argument, referencing this codebase patterns mirroring tool setups for the users specific requirements.
Usage: /prp-mcp-execute path/to/prp.md
---

# Execute MCP Server PRP

Execute a comprehensive Product Requirement Prompt (PRP) for building Model Context Protocol (MCP) servers with authentication, database integration, and Cloudflare Workers deployment.

PRP to execute: $ARGUMENTS

## Purpose

Execute MCP server PRPs with comprehensive validation, testing, and deployment verification following the proven patterns from this codebase.

## Execution Process

1. **Load & Analyze PRP**
   - Read the specified PRP file completely
   - Understand all context, requirements, and validation criteria
   - Create comprehensive todo list using TodoWrite tool
   - Identify all dependencies and integration points

2. **Context Gathering & Research**
   - Use Task agents to research existing MCP server patterns
   - Study authentication flows and database integration patterns
   - Research Cloudflare Workers deployment and environment setup
   - Gather all necessary documentation and code examples

3. **Implementation Phase**
   - Execute all implementation tasks in the correct order
   - Follow TypeScript patterns from the existing codebase
   - Implement MCP tools, resources, and authentication flows
   - Add comprehensive error handling and logging

## Notes

- Uses TodoWrite tool for comprehensive task management
- Follows all patterns from the proven codebase implementation
- Includes comprehensive error handling and recovery
- Optimized for Claude Code's validation loops
- Production-ready with monitoring and logging
- Compatible with MCP Inspector and Claude Desktop
