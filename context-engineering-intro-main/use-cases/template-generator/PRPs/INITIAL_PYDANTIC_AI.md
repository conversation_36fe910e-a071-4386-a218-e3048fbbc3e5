# Template Generation Request

## TECHNOLOGY/FRAMEWORK:

**Example:** CrewAI multi-agent systems  

**Your technology:** Pydantic AI agents

---

## TEMPLATE PURPOSE:

**What specific use case should this template be optimized for?**

**Your purpose:** Building intelligent AI agents with tool integration, conversation handling, and structured data validation using Pydantic AI framework

---

## CORE FEATURES:

**What are the essential features this template should help developers implement?**

**Your core features:**

- Agent creation with different model providers (OpenAI, Anthropic, Gemini)
- Tool integration patterns (web search, file operations, API calls)
- Conversation memory and context management
- Structured output validation with Pydantic models
- Error handling and retry mechanisms
- Testing patterns for AI agent behavior

---

## EXAMPLES TO INCLUDE:

**What working examples should be provided in the template?**

**Your examples:**

- Basic chat agent with memory
- Tool-enabled agent (web search + calculator)
- Multi-step workflow agent
- Agent with custom Pydantic models for structured outputs
- Testing examples for agent responses and tool usage

---

## DOCUMENTATION TO RESEARCH:

**What specific documentation should be thoroughly researched and referenced?**

**Your documentation:**
- https://ai.pydantic.dev/ - Official Pydantic AI documentation
- Model provider APIs (OpenAI, Anthropic) for integration patterns
- Tool integration best practices and examples

---

## DEVELOPMENT PATTERNS:

**What specific development patterns, project structures, or workflows should be researched and included?**

**Your development patterns:**
- How to structure agent modules and tool definitions
- Configuration management for different model providers
- Environment setup for development vs production
- Logging and monitoring patterns for AI agents

---

## SECURITY & BEST PRACTICES:

**What security considerations and best practices are critical for this technology?**

**Your security considerations:**
- API key management
- Input validation and sanitization for agent inputs
- Rate limiting and usage monitoring
- Prompt injection prevention
- Cost control and monitoring for model usage

---

## COMMON GOTCHAS:

**What are the typical pitfalls, edge cases, or complex issues developers face with this technology?**

**Your gotchas:**
- Handling model provider rate limits and errors
- Managing conversation state across requests
- Tool execution error handling and retries

---

## VALIDATION REQUIREMENTS:

**What specific validation, testing, or quality checks should be included in the template?**

**Your validation requirements:**
- Tool unit testing testing
- Agent unit testing
